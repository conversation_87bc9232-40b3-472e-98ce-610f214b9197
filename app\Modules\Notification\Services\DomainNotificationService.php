<?php

namespace App\Modules\Notification\Services;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\Notification\Constants\NotificationType;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DomainNotificationService
{
    public static function instance(): self
    {
        return new self;
    }

    public function domainRequestCreatedNewRegistered(string $domain, $userId): void
    {
        $this->sendUserNotification([
            'user_id' => $userId,
            'title' => 'Domain Deletion Request Created',
            'message' => "A deletion request for your domain \"{$domain}\" has been created by admin and is being processed immediately.",
            'redirect_url' => '/domain',
            'importance' => NotificationType::IMPORTANT,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function domainRequestCreated(string $domain, $userId): void
    {
        $this->sendUserNotification([
            'user_id' => $userId,
            'title' => 'Domain Deletion Request Created',
            'message' => "A deletion request for your domain \"{$domain}\" has been created by admin and approved. The deletion process will take 1-2 days to complete.",
            'redirect_url' => '/domain',
            'importance' => NotificationType::IMPORTANT,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function domainRequestApproved(string $domain, $userId): void
    {
        $this->sendUserNotification([
            'user_id' => $userId,
            'title' => 'Domain Deletion Request Approved',
            'message' => "Your deletion request for domain \"{$domain}\" has been approved and is being processed.",
            'redirect_url' => 'domain',
            'importance' => NotificationType::IMPORTANT,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function domainRequestRejected(string $domain, $userId, string $reason = ''): void
    {
        $message = "Your deletion request for domain \"{$domain}\" has been rejected.";
        if (!empty($reason)) {
            $message .= " Reason: {$reason}";
        }

        $this->sendUserNotification([
            'user_id' => $userId,
            'title' => 'Domain Deletion Request Rejected',
            'message' => $message,
            'redirect_url' => 'domain',
            'importance' => NotificationType::IMPORTANT,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function domainRequestCancelled(string $domain, $userId): void
    {
        $this->sendUserNotification([
            'user_id' => $userId,
            'title' => 'Domain Deletion Request Cancelled',
            'message' => "Your deletion request for domain \"{$domain}\" has been cancelled.",
            'redirect_url' => 'domain',
            'importance' => NotificationType::MEDIUM,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function domainDeletionCompleted(string $domain, $userId): void
    {
        $this->sendUserNotification([
            'user_id' => $userId,
            'title' => 'Domain Deletion Completed',
            'message' => "Your domain \"{$domain}\" has been successfully deleted.",
            'redirect_url' => 'domain',
            'importance' => NotificationType::IMPORTANT,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    public function domainDeletionFailed(string $domain, $userId, string $reason = ''): void
    {
        $message = "The deletion of your domain \"{$domain}\" has failed.";
        if (!empty($reason)) {
            $message .= " Reason: {$reason}";
        }

        $this->sendUserNotification([
            'user_id' => $userId,
            'title' => 'Domain Deletion Failed',
            'message' => $message,
            'redirect_url' => 'domain',
            'importance' => NotificationType::CRITICAL,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);
    }

    /**
     * Send notification to user notifications table
     */
    private function sendUserNotification(array $data): bool
    {
        return DB::client()->table('notifications')->insert($data);
    }
}
