<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\RequestDelete\Jobs\SendDomainDeleteRequestEmail;
use App\Events\DomainHistoryEvent;
use Carbon\Carbon;

class DomainDeletionApprovalService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function handleApproval(array $data, array $adminContext, string $supportNote): void
    {
        $this->logDomainHistory($data, 'DOMAIN_UPDATED', 'success', 'Domain deletion request approved by ' . $adminContext['name'] . ' (' . $adminContext['email'] . ')');
        $this->markAsApproved($data, $adminContext, $supportNote);

        $isRecentDomain = $this->isRecentlyRegisteredDomain($data['domainId']);
        $this->processApprovalByDomainAge($data, $isRecentDomain);
        $this->sendApprovalNotifications($data, $isRecentDomain);
    }

    public function markAsApproved(array $data, array $adminContext, string $supportNote): void
    {
        $registeredDomainId = $this->getRegisteredDomainId($data['domainId']);
        $refundStatus = $this->calculateRefundStatusForApproval($data['domainId'], $registeredDomainId);
        $approvalData = $this->buildApprovalData($adminContext, $supportNote, $refundStatus);
        DatabaseQueryService::instance()->updateDomainCancellationRequest($registeredDomainId, $approvalData);
    }

    private function calculateRefundStatusForApproval(string $domainId, int $registeredDomainId): array
    {
        $requestInfo = DatabaseQueryService::instance()->getRequestDetailsForRenewal($registeredDomainId);
        return DatabaseQueryService::instance()->calculateRefundAndRenewalStatus($domainId, $requestInfo->requested_at);
    }

    private function buildApprovalData(array $adminContext, string $supportNote, array $refundStatus): array
    {
        $adminFullName = "{$adminContext['name']} ({$adminContext['email']})";

        return [
            'support_agent_id' => $adminContext['id'],
            'support_agent_name' => $adminFullName,
            'feedback_date' => now(),
            'support_note' => $supportNote,
            'is_refunded' => $refundStatus['is_refunded'],
            'is_renewal' => $refundStatus['is_renewal'],
            'deleted_at' => now(),
        ];
    }

    private function processApprovalByDomainAge(array $data, bool $isRecentDomain): void
    {
        if ($isRecentDomain) {
            $this->dispatchDeletionJob($data);
        }
    }

    private function sendApprovalNotifications(array $data, bool $isRecentDomain): void
    {
        if ($isRecentDomain) {
            $this->sendImmediateApprovalNotifications($data);
        } else {
            $this->sendNormalApprovalNotifications($data);
        }
    }

    private function dispatchDeletionJob(array $data): void
    {
        DatabaseQueryService::instance()->dispatchDeletionJob($data);
    }

    private function sendImmediateApprovalNotifications(array $data): void
    {
        $this->sendUserNotification($data, 'Domain Deletion Request Approved', 'Your request to delete the domain "' . $data['domainName'] . '" has been approved and is being processed immediately.');
        $this->sendUserEmail(
            $data,
            'Domain Deletion Request Approved',
            'This email is to confirm that you have requested to delete the domain with the following details:',
            'Your domain deletion is being processed immediately as it was recently registered.',
        );
    }

    private function sendNormalApprovalNotifications(array $data): void
    {
        $this->sendUserNotification($data, 'Domain Deletion Request Approved', 'Your request to delete the domain "' . $data['domainName'] . '" has been approved. The deletion process will take 1-2 days to complete.');
        $this->sendUserEmail(
            $data,
            'Domain Deletion Request Approved',
            'This email is to confirm that you have requested to delete the domain with the following details:',
            'If you did not request this deletion, please contact our support team immediately to cancel the process.',
            'If you wish to proceed, no further action is required—your request will be completed as scheduled.'
        );
    }

    private function sendUserNotification(array $data, string $title, string $message): void
    {
        DatabaseQueryService::instance()->insertUserNotification($data, $title, $message);
    }

    private function sendUserEmail(array $data, string $subject, string $body, ?string $body2 = null, ?string $body3 = null, ?string $body4 = null): void
    {
        if (!isset($data['userEmail']) || !isset($data['domainName']))
            return;

        SendDomainDeleteRequestEmail::dispatch($data, $subject, $body, $body2, $body3, $body4);
    }

    private function logDomainHistory(array $data, string $action, string $status, string $message): void
    {
        event(new DomainHistoryEvent(['domain_id' => $data['domainId'], 'type' => $action, 'status' => $status, 'user_id' => $data['userId'] ?? null, 'message' => $message, 'payload' => json_encode($data),]));   
    }

    private function getRegisteredDomainId(string $domainId): int
    {
        return DatabaseQueryService::instance()->getRegisteredDomainId($domainId);
    }

    private function isRecentlyRegisteredDomain(string $domainId): bool
    {
        return DatabaseQueryService::instance()->isRecentlyRegisteredDomain($domainId);
    }
}
