<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\RequestDelete\Jobs\DomainEppCancellation;
use App\Modules\RequestDelete\Jobs\DomainRejectRequestJob;
use App\Modules\RequestDelete\Jobs\SendDomainDeleteRequestEmail;
use App\Modules\Client\Constants\DomainStatus;
use App\Events\DomainHistoryEvent;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\RequestDelete\Services\DomainDeletionApprovalService;
use App\Modules\Notification\Services\DomainNotificationService;
use App\Modules\RequestDelete\Services\DomainRegistrationRefundLimitService;

class DomainDeleteService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }
    public function createDeleteRequest($requestOrData)
    {
        $data = is_array($requestOrData) ? $requestOrData : $requestOrData->all();
        $this->processCreateRequest($data);
        return ['status' => 'success', 'message' => 'Domain deletion request created successfully'];
    }

    public function approveDeleteRequest($request)
    {
        return $this->processRequestAction($request, 'approve');
    }

    public function rejectDeleteRequest($request)
    {
        return $this->processRequestAction($request, 'reject');
    }

    public function cancelDeleteRequest($request)
    {
        return $this->processRequestAction($request, 'cancel');
    }

    private function processRequestAction($request, string $action)
    {
        $domainInfo = $this->getDomainInfoForRequest($request->domainId);
        $adminContext = $this->getAdminContext();
        $supportNote = $request->support_note ?? "Request {$action} by {$adminContext['email']}";
        $data = $this->prepareDomainData($domainInfo, $supportNote, $adminContext);
        $requestData = $request->all();

        if (isset($requestData['supportNote'])) {
            $data['supportNote'] = $requestData['supportNote'];
        }

        switch ($action) {
            case 'approve': // aproved request
                $this->processApproveRequest($data);
                return ['status' => 'success', 'message' => 'Domain deletion request approved successfully'];
            case 'reject': // reject request
                $this->processRejectRequest($data);
                return ['status' => 'success', 'message' => 'Domain deletion request rejected successfully'];
            case 'cancel': // cancel request
                $this->processCancelRequest($data);
                return ['status' => 'success', 'message' => 'Domain deletion request canceled successfully'];
            default:
                throw new \InvalidArgumentException("Invalid action: {$action}");
        }
    }

    private function prepareDomainData($domainInfo, string $supportNote, array $adminContext): array
    {
        return [
            'domainId' => $domainInfo->domainId,
            'domainName' => $domainInfo->domainName,
            'userId' => $domainInfo->userID,
            'userEmail' => $domainInfo->userEmail,
            'userName' => trim(($domainInfo->first_name ?? '') . ' ' . ($domainInfo->last_name ?? '')) ?: 'Unknown User',
            'reason' => $domainInfo->reason,
            'supportNote' => $supportNote,
            'createdDate' => $domainInfo->requested_at ?? now()->toDateTimeString(),
            'adminId' => $adminContext['id'],
            'adminName' => $adminContext['name'],
            'adminEmail' => $adminContext['email']
        ];
    }

    private function getDomainInfoForRequest($domainId)
    {
        $domainInfo = DatabaseQueryService::instance()->getDomainInfo($domainId);

        if (!$domainInfo) {
            app(AuthLogger::class)->error("Domain info not found for domainId: {$domainId}.");
            throw new \Exception("Domain cancellation request not found for domain ID: {$domainId}");
        }

        return $domainInfo;
    }

    public function processExpiredRequests()
    {
        $approvedRequests = DatabaseQueryService::instance()->getExpiredApprovedRequests();

        foreach ($approvedRequests as $request) {
            $data = $this->prepareRequestData($request);
            $this->processApprovedRequest($data);
        }

        return count($approvedRequests);
    }

    // PRIVATE FUNCTION

    private function prepareRequestData($request): array
    {
        return [
            'domainId' => $request->domainId,
            'domainName' => $request->domainName,
            'userId' => $request->userID,
            'userEmail' => $request->userEmail,
            'reason' => $request->reason,
            'createdDate' => $request->approvedDate,
            'adminId' => $request->support_agent_id,
            'adminName' => 'System',
            'adminEmail' => '<EMAIL>',
            'supportNote' => "The Deletion processed automatically after 24+ hours"
        ];
    }

    private function processCreateRequest(array $data): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = "Request delete created by {$adminContext['email']}";

        $this->updateDomainStatusDeletion($data['domainId']);
        $this->createDomainCancellationRequest($data, $adminContext, $supportNote);

        if ($this->isRecentlyRegisteredDomain($data['domainId'])) {
            $processData = $this->buildCreateRequestProcessData($data, $adminContext, $supportNote);
            $this->dispatchDeletionJob($processData);
            $registeredDomainId = $this->getRegisteredDomainId($data['domainId']);
            DomainRegistrationRefundLimitService::instance()->incrementCounter($registeredDomainId);
            DomainNotificationService::instance()->domainRequestCreatedNewRegistered($data['domainName'], $data['userId']);
        } else {
            DomainNotificationService::instance()->domainRequestCreated($data['domainName'], $data['userId']);
            $this->sendUserEmail($data, 'Domain Deletion Request Created', 'A deletion request for your domain "' . $data['domainName'] . '" has been created by admin and approved. The deletion process will take 1-2 days to complete. This action is final and cannot be undone.');
        }
    }

    private function buildCreateRequestProcessData(array $data, array $adminContext, string $supportNote): array
    {
        return array_merge($data, [
            'supportNote' => $supportNote,
            'adminId' => $adminContext['id'],
            'adminName' => $adminContext['name'],
            'adminEmail' => $adminContext['email']
        ]);
    }

    private function processApproveRequest(array $data): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = isset($data['supportNote']) ? $data['supportNote'] : "Request delete approved by {$adminContext['email']}";

        DomainDeletionApprovalService::instance()->handleApproval($data, $adminContext, $supportNote);
    }

    private function processRejectRequest(array $data): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = $data['supportNote'] ?? "Request delete rejected by {$adminContext['email']}";

        $this->rejectDomainDeletionRequest($data, $adminContext, $supportNote);
        $this->dispatchRejectRequestJob($data, $adminContext, $supportNote);
        $this->sendRejectionNotification($data, $adminContext);
        event(new DomainHistoryEvent(['domain_id' => $data['domainId'], 'type' => 'DOMAIN_UPDATED', 'status' => 'success', 'user_id' => $data['userId'] ?? null, 'message' => 'Domain deletion request rejected by ' . $adminContext['name'] . ' (' . $adminContext['email'] . ')', 'payload' => json_encode($data),]));
    }

    private function processCancelRequest(array $data): void
    {
        $adminContext = $this->getAdminContext();
        $supportNote = $data['supportNote'] ?? "Request delete canceled by {$adminContext['email']}";

        // $registeredDomainId = $this->getRegisteredDomainId($data['domainId']);
        // DomainRegistrationRefundLimitService::instance()->decrementCounter($registeredDomainId);

        $this->cancelDomainDeletionRequest($data, $adminContext, $supportNote);
        $this->dispatchRejectRequestJob($data, $adminContext, $supportNote);
        $this->sendCancellationNotification($data, $adminContext);
        event(new DomainHistoryEvent(['domain_id' => $data['domainId'], 'type' => 'DOMAIN_UPDATED', 'status' => 'success', 'user_id' => $data['userId'] ?? null, 'message' => 'Domain deletion request canceled by ' . $adminContext['name'] . ' (' . $adminContext['email'] . ')', 'payload' => json_encode($data),]));
    }

    private function isRecentlyRegisteredDomain(string $domainId): bool
    {
        return DatabaseQueryService::instance()->isRecentlyRegisteredDomain($domainId);
    }


    private function processApprovedRequest(array $data): void
    {
        $this->dispatchDeletionJob($data);
    }

    private function dispatchDeletionJob(array $data): void
    {
        DatabaseQueryService::instance()->dispatchDeletionJob($data);
    }

    private function getAdminContext(): array
    {
        return [
            'id' => Auth::id() ?? null,
            'name' => Auth::user()->name ?? 'System',
            'email' => Auth::user()->email ?? 'System'
        ];
    }

    private function getRegisteredDomainId($domainId): int
    {
        return DatabaseQueryService::instance()->getRegisteredDomainId($domainId);
    }

    private function dispatchRejectRequestJob(array $data, array $adminContext, string $supportNote): void
    {
        DomainRejectRequestJob::dispatch(
            $data['domainId'],
            $data['domainName'],
            $data['userId'],
            $data['userEmail'],
            $data['reason'] ?? 'Domain deletion request rejected/canceled',
            $data['createdDate'] ?? now()->toDateTimeString(),
            $supportNote,
            $adminContext['id'],
            $adminContext['name'],
            $adminContext['email']
        );
    }

    private function updateDomainStatusDeletion($domainId): void
    {
        DatabaseQueryService::instance()->updateDomainStatus($domainId, DomainStatus::IN_PROCESS);
    }

    private function createDomainCancellationRequest(array $data, array $adminContext, string $supportNote): void
    {
        $adminFullName = "{$adminContext['name']} ({$adminContext['email']})";
        $registeredDomainId = $this->getRegisteredDomainId($data['domainId']);

        $requestedAt = now();
        $refundStatus = DatabaseQueryService::instance()->calculateRefundAndRenewalStatus($data['domainId'], $requestedAt);

        DatabaseQueryService::instance()->createDomainCancellationRequest([
            'registered_domain_id' => $registeredDomainId,
            'reason' => $data['reason'] ?? 'Domain deletion request by ' . $adminFullName,
            'requested_at' => $requestedAt,
            'support_agent_id' => $adminContext['id'],
            'support_agent_name' => $adminFullName,
            'feedback_date' => now(),
            'support_note' => $supportNote,
            'deleted_at' => now(),
            'is_refunded' => $refundStatus['is_refunded'],
            'is_renewal' => $refundStatus['is_renewal'],
        ]);
    }

    private function rejectDomainDeletionRequest(array $domainInfo, array $adminContext, string $supportNote): void
    {
        $registeredDomainId = $this->getRegisteredDomainId($domainInfo['domainId']);
        $rejectionData = $this->buildRejectionData($adminContext, $supportNote);
        DatabaseQueryService::instance()->updateDomainCancellationRequest($registeredDomainId, $rejectionData);
    }

    private function cancelDomainDeletionRequest(array $domainInfo, array $adminContext, string $supportNote): void
    {
        $registeredDomainId = $this->getRegisteredDomainId($domainInfo['domainId']);
        $cancellationData = $this->buildCancellationData($adminContext, $supportNote);
        DatabaseQueryService::instance()->updateDomainCancellationRequest($registeredDomainId, $cancellationData);
    }

    private function buildRejectionData(array $adminContext, string $supportNote): array
    {
        return [
            'support_agent_id' => $adminContext['id'],
            'support_agent_name' => $adminContext['name'] . ' (' . $adminContext['email'] . ')',
            'support_note' => $supportNote,
            'feedback_date' => now(),
            'deleted_at' => null,
        ];
    }

    private function buildCancellationData(array $adminContext, string $supportNote): array
    {
        return [
            'support_agent_id' => $adminContext['id'],
            'support_agent_name' => $adminContext['name'] . ' (' . $adminContext['email'] . ')',
            'support_note' => $supportNote,
            'feedback_date' => null,
            'deleted_at' => now(),
        ];
    }

    private function sendUserNotification(array $data, string $title, string $message): void
    {
        DatabaseQueryService::instance()->insertUserNotification($data, $title, $message);
    }

    private function sendUserEmail(array $data, string $subject, string $body, ?string $body2 = null, ?string $body3 = null, ?string $body4 = null): void
    {
        if (!isset($data['userEmail']) || !isset($data['domainName']))
            return;

        SendDomainDeleteRequestEmail::dispatch($data, $subject, $body, $body2, $body3, $body4);
    }

    private function sendRejectionNotification(array $data, array $adminContext): void
    {
        $message = 'Your request to delete the domain "' . $data['domainName'] . '" has been rejected by ' . $adminContext['name'] . '.';
        $this->sendUserNotification($data, 'Domain Deletion Request Rejected', $message);
        $emailBody = 'Your request to delete the domain "' . $data['domainName'] . '" has been rejected by our support team. If you have any questions, please contact our support.';
        $this->sendUserEmail($data, 'Domain Deletion Request Rejected', $emailBody);
    }

    private function sendCancellationNotification(array $data, array $adminContext): void
    {
        $message = 'Your domain deletion request for "' . $data['domainName'] . '" has been canceled by ' . $adminContext['name'] . '.';
        $this->sendUserNotification($data, 'Domain Deletion Request Canceled', $message);
        $emailBody = 'Your domain deletion request for "' . $data['domainName'] . '" has been canceled by our support team.';
        $this->sendUserEmail($data, 'Domain Deletion Request Canceled', $emailBody);
    }
}
